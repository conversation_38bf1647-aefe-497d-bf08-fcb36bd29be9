# 小红书自动爬虫

基于Playwright实现的小红书内容爬取工具，支持搜索、内容提取、图片下载等功能。

## 功能特性

- ✅ **自动登录检测**: 智能检测登录状态，支持手动登录等待
- ✅ **关键词搜索**: 支持自定义搜索关键词
- ✅ **内容爬取**: 提取帖子标题、内容、作者、发布时间、点赞数等信息
- ✅ **图片下载**: 自动下载帖子中的图片到本地，按帖子标题分文件夹存储
- ✅ **数据过滤**: 智能过滤页面框架内容，只保留有效信息
- ✅ **反爬虫机制**: 随机延时、模拟真实用户行为
- ✅ **智能去重**: 自动记录已爬取帖子，避免重复爬取
- ✅ **数据库存储**: 支持SQLite数据库存储，自动记录搜索关键词
- ✅ **数据迁移**: 支持将现有JSON数据迁移到数据库
- ✅ **配置化**: 支持配置文件自定义各种参数
- ✅ **错误处理**: 完善的错误处理和重试机制
- ✅ **日志记录**: 详细的操作日志记录

## 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt

# 安装Playwright浏览器
playwright install chromium
```

## 使用方法

### 基本使用

```bash
# 使用默认配置运行（搜索"笑话"，爬取2个帖子）
python xiaohongshu_crawler.py

# 指定搜索关键词和数量
python xiaohongshu_crawler.py --keyword "美食" --count 5

# 无头模式运行
python xiaohongshu_crawler.py --headless

# 调试模式
python xiaohongshu_crawler.py --debug

# 清空已爬取记录
python xiaohongshu_crawler.py --clear-records

# 禁用去重功能
python xiaohongshu_crawler.py --disable-dedup
```

### 数据库功能

项目现在支持SQLite数据库存储，自动记录搜索关键词：

```bash
# 数据迁移：将现有JSON数据导入数据库
python migrate_json_to_db.py --current-dir --keyword "笑话"

# 迁移指定文件
python migrate_json_to_db.py --input xiaohongshu_crawl_results_20250802_172004.json --keyword "美食"

# 迁移指定目录
python migrate_json_to_db.py --input ./data --keyword "旅游"

# 测试数据库功能
python test_database_functionality.py
```

### 命令行参数

- `--keyword, -k`: 搜索关键词
- `--count, -c`: 爬取帖子数量
- `--config`: 配置文件路径（默认：config.json）
- `--headless`: 无头模式运行
- `--debug, -d`: 调试模式，输出更多信息
- `--clear-records`: 清空已爬取记录后退出
- `--disable-dedup`: 禁用去重功能

### 配置文件

编辑 `config.json` 文件可以自定义各种参数：

```json
{
  "crawler_settings": {
    "default_keyword": "笑话",
    "default_post_count": 2,
    "headless_mode": false,
    "timeout_ms": 30000,
    "login_wait_time": 300,
    "delay_between_posts": 2,
    "delay_after_click": 3,
    "page_load_delay": 3,
    "login_check_interval": 5
  },
  "selectors": {
    "search_input": "#search-input",
    "note_items": ".note-item",
    // ... 更多选择器配置
  },
  "output_settings": {
    "output_file": "xiaohongshu_crawl_results.json",
    "images_folder": "images",
    "include_timestamp": true
  },
  "image_download": {
    "enabled": true,
    "max_images_per_post": 10,
    "timeout_seconds": 30
  },
  "database": {
    "enabled": true,
    "db_path": "xiaohongshu_crawl.db",
    "use_database": true,
    "fallback_to_json": true
  },
  "deduplication": {
    "enabled": true,
    "storage_file": "crawled_posts.json",
    "storage_format": "json"
  }
}
```

## 数据存储

### 数据库存储（推荐）

爬取结果自动保存到SQLite数据库中，包含以下字段：

- `post_id`: 帖子ID（唯一标识）
- `search_keyword`: 搜索关键词（新增字段）
- `url`: 帖子URL
- `title`: 帖子标题
- `content`: 帖子内容
- `author`: 作者名称
- `publish_time`: 发布时间
- `like_count`: 点赞数
- `comment_count`: 评论数
- `tags`: 标签列表（JSON格式）
- `images`: 图片URL列表（JSON格式）
- `downloaded_images`: 本地图片路径列表（JSON格式）
- `crawl_time`: 爬取时间
- `created_at`: 记录创建时间

### JSON格式（备份）

同时支持JSON格式输出，数据结构如下：

```json
[
  {
    "postId": "67ae0cb2000000002803e09d",
    "search_keyword": "笑话",
    "url": "https://www.xiaohongshu.com/explore/67ae0cb2000000002803e09d",
    "title": "帖子标题",
    "content": "帖子内容",
    "author": "作者名称",
    "publishTime": "发布时间",
    "likeCount": "点赞数",
    "commentCount": "评论数",
    "tags": ["#标签1", "#标签2"],
    "images": ["图片URL1", "图片URL2"],
    "downloaded_images": ["本地图片路径1", "本地图片路径2"],
    "crawl_time": "2025-08-02T17:00:00"
  }
]
```

## 图片下载功能

程序会自动下载帖子中的图片，并按以下规则组织：

1. **文件夹结构**: `images/帖子标题_帖子ID前8位/`
2. **文件命名**: `image_01.jpg`, `image_02.webp` 等
3. **支持格式**: JPG, PNG, WebP
4. **自动清理**: 文件夹名会自动移除非法字符
5. **防重名**: 添加帖子ID前缀避免重名

示例文件结构：
```
images/
├── 上班别看，容易笑喷，直呼有病_65f53bd2/
│   ├── image_01.webp
│   ├── image_02.webp
│   └── image_03.jpg
└── 一听就会哈哈哈哈哈哈哈哈的压箱底笑话_6690d8de/
    ├── image_01.jpg
    └── image_02.webp
```

## 智能去重功能

程序会自动记录已爬取的帖子，避免重复爬取，提高效率：

### 功能特性

1. **自动记录**: 每次成功爬取帖子后，自动保存帖子ID到 `crawled_posts.json`
2. **智能过滤**: 在搜索结果页面自动过滤掉已爬取的帖子
3. **持久化存储**: 即使程序异常退出，已记录的帖子ID也不会丢失
4. **详细记录**: 保存帖子ID、爬取时间、标题、作者等信息

### 存储格式

```json
{
  "67ae0cb2000000002803e09d": {
    "crawl_time": "2025-08-02T17:00:00",
    "title": "上班别看，容易笑喷，直呼有病",
    "author": "风言风语文案"
  },
  "65f53bd2000000001203ecc9": {
    "crawl_time": "2025-08-02T17:05:00",
    "title": "一听就会哈哈哈哈哈哈哈哈的压箱底笑话",
    "author": "吱吱吱"
  }
}
```

### 管理命令

```bash
# 查看已爬取记录数量（在日志中显示）
python xiaohongshu_crawler.py --debug

# 清空所有已爬取记录
python xiaohongshu_crawler.py --clear-records

# 临时禁用去重功能
python xiaohongshu_crawler.py --disable-dedup
```

### 使用场景

- **多次运行**: 可以多次运行爬虫而不会重复爬取相同内容
- **增量爬取**: 定期运行爬虫，只爬取新发布的内容
- **关键词切换**: 切换不同关键词时，已爬取的帖子会被自动跳过

## 测试功能

运行测试脚本验证环境配置：

```bash
python test_crawler.py
```

测试内容包括：
- 配置文件加载
- 依赖包检查
- 图片文件夹创建
- 爬虫初始化

## 注意事项

1. **登录要求**: 首次运行需要手动登录小红书账号
2. **反爬虫**: 程序已内置反爬虫机制，但仍需适度使用
3. **网络环境**: 确保网络连接稳定
4. **浏览器**: 需要安装Chromium浏览器（通过playwright install安装）
5. **存储空间**: 图片下载会占用本地存储空间，请确保有足够空间

## 文件结构

```
xhs-craw/
├── xiaohongshu_crawler.py      # 主爬虫脚本
├── database_manager.py         # 数据库管理模块
├── migrate_json_to_db.py       # 数据迁移脚本
├── test_database_functionality.py  # 数据库功能测试脚本
├── config.json                 # 配置文件
├── requirements.txt            # Python依赖
├── README.md                   # 使用说明
├── xiaohongshu_crawl.db        # SQLite数据库文件
├── crawled_posts.json          # 已爬取帖子记录（去重，兼容模式）
├── images/                     # 图片下载目录
│   ├── 帖子标题1_postid/       # 按帖子标题分文件夹
│   │   ├── image_01.jpg
│   │   └── image_02.webp
│   └── 帖子标题2_postid/
├── xiaohongshu_crawler.log     # 运行日志
├── migration.log               # 数据迁移日志
└── xiaohongshu_crawl_results_*.json  # 爬取结果（JSON备份）
```

## 开发说明

本项目分为两个阶段开发：

1. **阶段一**: 手动演示和记录过程（详见 `stage1_operation_record.md`）
2. **阶段二**: 编写自动化Python脚本（当前版本）

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和网站使用条款。